using AutoMapper;
using BASSUmlBusiness.Extentions;
using BASSUmlBusiness.Models;
using BASSUmlBusiness.Pagination;
using BASSUmlBusiness.Services.Abstractions;
using BASSUmlRest.Attributes;
using BASSUmlRest.DTOs;
using BASSUmlRest.DTOs.PermissionDTOs;
using BASSUmlRest.DTOs.ProjectDTOs;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace BASSUmlRest.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ProjectController : ControllerBase
    {
        private readonly IProjectService _projectService;
        private readonly IPermissionService _permissionService;
        private readonly IMapper _mapper;
        public ProjectController(IProjectService projectService, IMapper mapper, IPermissionService permissionService)
        {
            _projectService = projectService;
            _mapper = mapper;
            _permissionService = permissionService;
        }

        /// <summary>
        /// Get a project with its associated diagrams and template classes and associated attributes.
        /// </summary>
        /// <returns>Project</returns>
        /// <response code="500">When got any error in database</response>
        /// <response code="200">Returns a of JSON of project object </response>
        [HttpGet("{idProject}")]
        [RequireViewAccess]
        public ActionResult<ProjectDetailsDTO> GetProjectWithDiagramsAndClasses(int idProject)
        {
            string? email = User.GetEmployeeEmail() ?? User.GetEmail();
            Project project = _projectService.GetProjectWithDiagramsAndClasses(idProject, email);
            return Ok(_mapper.Map<Project, ProjectDetailsDTO>(project));
        }

        /// <summary>
        /// Get all projects for login user with pagination, sorting, and filtering.
        /// </summary>
        /// <param name="paginationQuery">Pagination, sorting, and filtering parameters</param>
        /// <param name="currentPage">Current page number (default: 1)</param>
        /// <returns>List of <see cref="ProjectLockAccessDTO">projects</see> with accesstype</returns>
        /// <response code="500">When got any error in database</response>
        /// <response code="200">Returns list of JSON of project </response>
        [HttpGet]
        public ActionResult<IList<ProjectLockAccessDTO>> GetAllProjectsByUser(
            [FromQuery] PaginationQuery? paginationQuery,
            [FromQuery] int currentPage = 1,
            [FromQuery] string? sortBy = null,
            [FromQuery] bool sortDescending = true,
            [FromQuery] string? searchTerm = null,
            [FromQuery] List<string>? projectTypes = null,
            [FromQuery] List<string>? productLines = null,
            [FromQuery] bool? isSelfProject = null)
        {
            // Create pagination filter from query
            PaginationFilter paginationFilter = _mapper.Map<PaginationQuery, PaginationFilter>(paginationQuery ?? new PaginationQuery());

            // Apply additional query parameters if they were provided directly
            if (!string.IsNullOrEmpty(sortBy))
            {
                paginationFilter.SortBy = sortBy;
                paginationFilter.SortDescending = sortDescending;
            }

            if (!string.IsNullOrEmpty(searchTerm))
            {
                paginationFilter.SearchTerm = searchTerm;
            }

            if (projectTypes != null && projectTypes.Any())
            {
                paginationFilter.ProjectTypes = projectTypes;
            }

            if (productLines != null && productLines.Any())
            {
                paginationFilter.ProductLines = productLines;
            }

            if (isSelfProject.HasValue)
            {
                paginationFilter.IsSelfProject = isSelfProject.Value;
            }

            // Get projects with filtering and sorting applied
            IList<Project> projects = _projectService.GetAllProjectsByUser(
                User.GetEmployeeEmail() ?? User.GetEmail(),
                paginationFilter,
                currentPage);

            // Add pagination info to response headers
            Response.Headers.Add("X-Pagination", JsonConvert.SerializeObject(_mapper.Map<PaginationFilter, PaginationFilterDTO>(paginationFilter)));

            return Ok(_mapper.Map<IList<Project>, IList<ProjectLockAccessDTO>>(projects));
        }

        /// <summary>
        /// Create a project
        /// </summary>
        /// <param name="project">Require a Project as JSON</param>
        /// <returns>Newly created <see cref="ProjectDTO">Project</see> object </returns>
        /// <response code="500">When got any error in database</response>
        /// <response code="200">Returns a JSON of Project object</response>
        [HttpPost]
        public ActionResult<ProjectCreateDTO> CreateProject([FromBody] ProjectDTO project)
        {
            Project createdroject = _projectService.CreateProject(_mapper.Map<ProjectDTO, Project>(project), User.GetEmployeeEmail() ?? User.GetEmail());
            return Ok(_mapper.Map<Project, ProjectCreateDTO>(createdroject));
        }

        /// <summary>
        /// Update an existing project data
        /// </summary>
        /// <param name="project">Require a project object as JSON</param>
        /// <returns>Newly updated <see cref="ProjectDTO">Project</see> object </returns>
        /// <response code="404">When not found the object of the id in database</response>
        /// <response code="500">When got any error in database</response>
        /// <response code="200">Returns a JSON of Project object</response>
        [HttpPatch]
        [RequireEditAccess("id")]
        public ActionResult<ProjectDTO> UpdateProject([FromBody] ProjectDTO project)
        {
            Project updatedProject = _projectService.UpdateProject(_mapper.Map<ProjectDTO, Project>(project));
            return Ok(_mapper.Map<Project, ProjectDTO>(updatedProject));
        }

        /// <summary>
        /// Delete a particular project depending on the id project parameter (cascade delete)
        /// </summary>
        /// <param name="idProject">Require a project Id</param>
        /// <returns>No Content Result</returns>
        /// <response code="204">Returns No Content Result</response>
        /// <response code="404">When not found the object of the id in database</response>
        /// <response code="500">When got any error in database</response>
        [HttpDelete("{idProject}")]
        [RequireAdminAccess]
        public ActionResult DeleteProject(int idProject)
        {
            _projectService.DeleteProject(idProject, User.GetEmployeeEmail() ?? User.GetEmail());
            return NoContent();
        }

        /// <summary>
        /// Get all permissions of the given project
        /// </summary>
        /// <param name="idProject">Require a project Id</param>
        /// <returns>list of permissions</returns>
        /// <response code="200">Returns a array of JSON of Permission object</response>
        /// <response code="404">When not found the object of the id in database</response>
        /// <response code="500">When got any error in database</response>
        [HttpGet("permissions/{idProject}")]
        [RequireAdminAccess]
        public ActionResult<IList<PermissionDTO>> GetAllPermissions(int idProject)
        {
            IList<Permission> permissions = _permissionService.GetPermissions(idProject);
            return Ok(_mapper.Map<IList<Permission>, IList<PermissionDTO>>(permissions));
        }

        /// <summary>
        /// Get permission for login user in the given project
        /// </summary>
        /// <param name="idProject">Require a project Id</param>
        /// <returns><see cref="PermissionDTO">Permission</see> object for the specific project of that user</returns>
        /// <response code="200">Returns a JSON object of Permission</response>
        /// <response code="404">When not found the object of the id in database</response>
        /// <response code="500">When got any error in database</response>
        [HttpGet("checkpermission/{idProject}")]
        public ActionResult<PermissionDTO> CheckPermission(int idProject)
        {
            Permission permission = _permissionService.GetProjectPermissionByUser(idProject, User.GetEmployeeEmail() ?? User.GetEmail());
            return Ok(_mapper.Map<Permission, PermissionDTO>(permission));
        }

    }
}


