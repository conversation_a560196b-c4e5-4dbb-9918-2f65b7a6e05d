import { TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { ProjectAccessService } from './project-access.service';
import { PermissionApiService } from '../api/permission-api.service';
import { AccessType, IProjectPermissionDTO } from 'src/app/shared/model/project';

describe('ProjectAccessService', () => {
  let service: ProjectAccessService;
  let mockPermissionApiService: jasmine.SpyObj<PermissionApiService>;

  const mockPermission: IProjectPermissionDTO = {
    id: 1,
    accessType: AccessType.Editor,
    email: '<EMAIL>'
  };

  beforeEach(() => {
    const spy = jasmine.createSpyObj('PermissionApiService', ['checkPermission']);

    TestBed.configureTestingModule({
      providers: [
        ProjectAccessService,
        { provide: PermissionApiService, use: spy }
      ]
    });

    service = TestBed.inject(ProjectAccessService);
    mockPermissionApiService = TestBed.inject(PermissionApiService) as jasmine.SpyObj<PermissionApiService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('hasProjectAccess', () => {
    it('should return true when user has sufficient access', (done) => {
      mockPermissionApiService.checkPermission.and.returnValue(of(mockPermission));

      service.hasProjectAccess(1, AccessType.Viewer).subscribe(result => {
        expect(result).toBe(true);
        done();
      });
    });

    it('should return false when user has insufficient access', (done) => {
      const viewerPermission: IProjectPermissionDTO = {
        ...mockPermission,
        accessType: AccessType.Viewer
      };
      mockPermissionApiService.checkPermission.and.returnValue(of(viewerPermission));

      service.hasProjectAccess(1, AccessType.Admin).subscribe(result => {
        expect(result).toBe(false);
        done();
      });
    });

    it('should return false when permission check fails', (done) => {
      mockPermissionApiService.checkPermission.and.returnValue(throwError('API Error'));

      service.hasProjectAccess(1, AccessType.Viewer).subscribe(result => {
        expect(result).toBe(false);
        done();
      });
    });
  });

  describe('getProjectPermission', () => {
    it('should fetch permission from API when not cached', (done) => {
      mockPermissionApiService.checkPermission.and.returnValue(of(mockPermission));

      service.getProjectPermission(1).subscribe(result => {
        expect(result).toEqual(mockPermission);
        expect(mockPermissionApiService.checkPermission).toHaveBeenCalledWith(1);
        done();
      });
    });

    it('should return cached permission when available', (done) => {
      mockPermissionApiService.checkPermission.and.returnValue(of(mockPermission));

      // First call to cache the permission
      service.getProjectPermission(1).subscribe(() => {
        // Second call should use cache
        service.getProjectPermission(1).subscribe(result => {
          expect(result).toEqual(mockPermission);
          expect(mockPermissionApiService.checkPermission).toHaveBeenCalledTimes(1);
          done();
        });
      });
    });

    it('should return null when API call fails', (done) => {
      mockPermissionApiService.checkPermission.and.returnValue(throwError('API Error'));

      service.getProjectPermission(1).subscribe(result => {
        expect(result).toBeNull();
        done();
      });
    });
  });

  describe('convenience methods', () => {
    beforeEach(() => {
      mockPermissionApiService.checkPermission.and.returnValue(of(mockPermission));
    });

    it('canViewProject should check for Viewer access', (done) => {
      service.canViewProject(1).subscribe(result => {
        expect(result).toBe(true);
        done();
      });
    });

    it('canEditProject should check for Editor access', (done) => {
      service.canEditProject(1).subscribe(result => {
        expect(result).toBe(true);
        done();
      });
    });

    it('canAdminProject should check for Admin access', (done) => {
      const adminPermission: IProjectPermissionDTO = {
        ...mockPermission,
        accessType: AccessType.Admin
      };
      mockPermissionApiService.checkPermission.and.returnValue(of(adminPermission));

      service.canAdminProject(1).subscribe(result => {
        expect(result).toBe(true);
        done();
      });
    });
  });

  describe('cache management', () => {
    it('should clear specific project permission from cache', (done) => {
      mockPermissionApiService.checkPermission.and.returnValue(of(mockPermission));

      // Cache the permission
      service.getProjectPermission(1).subscribe(() => {
        // Clear cache
        service.clearProjectPermissionCache(1);

        // Next call should fetch from API again
        service.getProjectPermission(1).subscribe(() => {
          expect(mockPermissionApiService.checkPermission).toHaveBeenCalledTimes(2);
          done();
        });
      });
    });

    it('should clear all permissions from cache', (done) => {
      mockPermissionApiService.checkPermission.and.returnValue(of(mockPermission));

      // Cache multiple permissions
      service.getProjectPermission(1).subscribe(() => {
        service.getProjectPermission(2).subscribe(() => {
          // Clear all cache
          service.clearAllPermissionCache();

          // Next calls should fetch from API again
          service.getProjectPermission(1).subscribe(() => {
            expect(mockPermissionApiService.checkPermission).toHaveBeenCalledTimes(3);
            done();
          });
        });
      });
    });
  });

  describe('validateMultipleProjectAccess', () => {
    it('should validate access for multiple projects', (done) => {
      mockPermissionApiService.checkPermission.and.returnValue(of(mockPermission));

      const projectPermissions = [
        { projectId: 1, requiredAccess: AccessType.Viewer },
        { projectId: 2, requiredAccess: AccessType.Editor }
      ];

      service.validateMultipleProjectAccess(projectPermissions).subscribe(results => {
        expect(results.size).toBe(2);
        expect(results.get(1)).toBe(true);
        expect(results.get(2)).toBe(true);
        done();
      });
    });
  });

  describe('refreshProjectPermission', () => {
    it('should clear cache and fetch fresh permission', (done) => {
      mockPermissionApiService.checkPermission.and.returnValue(of(mockPermission));

      // Cache the permission
      service.getProjectPermission(1).subscribe(() => {
        // Refresh should clear cache and fetch again
        service.refreshProjectPermission(1).subscribe(result => {
          expect(result).toEqual(mockPermission);
          expect(mockPermissionApiService.checkPermission).toHaveBeenCalledTimes(2);
          done();
        });
      });
    });
  });
});
