{"ast": null, "code": "import packageJson from 'package.json';\nexport const environment = {\n  production: false,\n  backEndUrl: 'https://localhost:44380/api',\n  authUrl: `${window.location.origin}/authentication`,\n  about: {\n    appName: 'UML Web Application',\n    version: packageJson.version,\n    gojsVersion: packageJson.dependencies.gojs,\n    releaseDate: '1747721940176'\n  },\n  licenseKey: '298647e0b66143c702d90676423d6bbc5cf07e34cb960ef2050047f4ec5c6d47719bed7859c19bc681ab1bfd1f2ec78d8ac73e29c345553ab238dad842e581f8b53126b0115b408aa15424c190ff29a9a92d74f690e674a6d27888f6eaf891cb5ceca7d71bcf5ebc2e2d0f66507dff4be0f28e69e904991f6d6dcaf7fbfbbf4afb6f729b96fb578f',\n  bearerToken: 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6ImJhYTY0ZWZjMTNlZjIzNmJlOTIxZjkyMmUzYTY3Y2M5OTQxNWRiOWIiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oCBQ506KU6CV_YMP7FLtEkqLzjGzpTBuDoUVUmTC847Bk_SwFyq1ktS3YSLyfxIuvNjLwIx64hIfd4Bxym5UhrdlbcXwvRl2UmkY6eiG1dvDzoxb48Pt0eeGQ6b80TlcNMJXny7SQoZMZ22WET0caGdRYxuHDD7gXME1xnRfAbPHF8HVT_KIALsVycJaw3nE9utj3bf75P8g0HrN_KwxnmuZv3aFkSSzK2G996V99JIWQUd9__dECv6og4rIyMAxIUFNJxOT2inP-c_t18QPopeBbgQZVNmUWpZLev8ZnYYNuRlvyZywkKsUVtjQiB6F1C73_hQMNWWpGR4fwUbT1Q'\n};", "map": {"version": 3, "names": ["packageJson", "environment", "production", "backEndUrl", "authUrl", "window", "location", "origin", "about", "appName", "version", "gojsVersion", "dependencies", "gojs", "releaseDate", "licenseKey", "bearerToken"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\environments\\environment.ts"], "sourcesContent": ["import packageJson from 'package.json';\r\n\r\nexport const environment = {\r\n  production: false,\r\n  backEndUrl: 'https://localhost:44380/api',\r\n  authUrl: `${window.location.origin}/authentication`,\r\n  about: {\r\n    appName: 'UML Web Application',\r\n    version: packageJson.version,\r\n    gojsVersion: packageJson.dependencies.gojs,\r\n    releaseDate: '1747721940176',\r\n  },\r\n  licenseKey:\r\n    '298647e0b66143c702d90676423d6bbc5cf07e34cb960ef2050047f4ec5c6d47719bed7859c19bc681ab1bfd1f2ec78d8ac73e29c345553ab238dad842e581f8b53126b0115b408aa15424c190ff29a9a92d74f690e674a6d27888f6eaf891cb5ceca7d71bcf5ebc2e2d0f66507dff4be0f28e69e904991f6d6dcaf7fbfbbf4afb6f729b96fb578f',\r\n  bearerToken:\r\n    'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6ImJhYTY0ZWZjMTNlZjIzNmJlOTIxZjkyMmUzYTY3Y2M5OTQxNWRiOWIiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oCBQ506KU6CV_YMP7FLtEkqLzjGzpTBuDoUVUmTC847Bk_SwFyq1ktS3YSLyfxIuvNjLwIx64hIfd4Bxym5UhrdlbcXwvRl2UmkY6eiG1dvDzoxb48Pt0eeGQ6b80TlcNMJXny7SQoZMZ22WET0caGdRYxuHDD7gXME1xnRfAbPHF8HVT_KIALsVycJaw3nE9utj3bf75P8g0HrN_KwxnmuZv3aFkSSzK2G996V99JIWQUd9__dECv6og4rIyMAxIUFNJxOT2inP-c_t18QPopeBbgQZVNmUWpZLev8ZnYYNuRlvyZywkKsUVtjQiB6F1C73_hQMNWWpGR4fwUbT1Q',\r\n};\r\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,cAAc;AAEtC,OAAO,MAAMC,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,6BAA6B;EACzCC,OAAO,EAAE,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,iBAAiB;EACnDC,KAAK,EAAE;IACLC,OAAO,EAAE,qBAAqB;IAC9BC,OAAO,EAAEV,WAAW,CAACU,OAAO;IAC5BC,WAAW,EAAEX,WAAW,CAACY,YAAY,CAACC,IAAI;IAC1CC,WAAW,EAAE;GACd;EACDC,UAAU,EACR,kRAAkR;EACpRC,WAAW,EACT;CACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}