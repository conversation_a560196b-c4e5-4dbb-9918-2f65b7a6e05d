# Comprehensive Access Control System Guide

This guide explains the comprehensive access control system implemented for project-based operations in the Bassetti UML application.

## Overview

The access control system provides multi-layered security with both frontend and backend validation:

1. **Frontend Route Guards** - Prevent unauthorized navigation
2. **Backend API Attributes** - Server-side permission validation
3. **Service Layer** - Centralized permission checking and caching
4. **Integration** - Seamless integration with existing project locking and authentication

## Access Levels

The system uses three access levels (defined in `AccessType` enum):

- **Admin (0)** - Full control: edit, delete, manage permissions
- **Editor (1)** - Can edit project content but not manage permissions
- **Viewer (2)** - Read-only access

*Note: Lower numeric values have higher access privileges*

## Frontend Implementation

### 1. Enhanced Access Guard

The `accessGuard` now performs actual permission checking:

```typescript
// Routes requiring specific access levels
{
  path: 'editor/:id/diagram/:idDiagram',
  component: DiagramEditorComponent,
  canActivate: [accessGuard] // Requires Editor access
}
```

**Features:**
- Automatic project ID extraction from route parameters
- Route-specific access level requirements
- Proper error handling and user feedback
- Integration with existing URL copying functionality

### 2. Project Access Service

A comprehensive service for permission management:

```typescript
// Check if user can edit a project
this.projectAccessService.canEditProject(projectId).subscribe(canEdit => {
  if (canEdit) {
    // Allow editing
  } else {
    // Show read-only view
  }
});

// Get detailed permission information
this.projectAccessService.getProjectPermission(projectId).subscribe(permission => {
  console.log('User access level:', permission?.accessType);
});
```

**Features:**
- Permission caching for performance
- Convenience methods for common checks
- Bulk permission validation
- Cache management and refresh capabilities

### 3. Usage Examples

```typescript
// In a component
export class ProjectComponent implements OnInit {
  constructor(private projectAccessService: ProjectAccessService) {}

  ngOnInit() {
    // Check multiple permissions at once
    const checks = [
      { projectId: 1, requiredAccess: AccessType.Editor },
      { projectId: 2, requiredAccess: AccessType.Viewer }
    ];

    this.projectAccessService.validateMultipleProjectAccess(checks)
      .subscribe(results => {
        console.log('Project 1 edit access:', results.get(1));
        console.log('Project 2 view access:', results.get(2));
      });
  }

  onProjectUpdate(projectId: number) {
    // Refresh permission after potential changes
    this.projectAccessService.refreshProjectPermission(projectId)
      .subscribe(permission => {
        // Handle updated permission
      });
  }
}
```

## Backend Implementation

### 1. Authorization Attributes

Server-side protection using custom attributes:

```csharp
[HttpGet("{idProject}")]
[RequireViewAccess] // Requires at least Viewer access
public ActionResult<ProjectDetailsDTO> GetProject(int idProject)

[HttpPatch]
[RequireEditAccess("id")] // Requires Editor access, project ID in "id" parameter
public ActionResult<ProjectDTO> UpdateProject([FromBody] ProjectDTO project)

[HttpDelete("{idProject}")]
[RequireAdminAccess] // Requires Admin access
public ActionResult DeleteProject(int idProject)
```

**Available Attributes:**
- `[RequireViewAccess]` - Minimum Viewer access
- `[RequireEditAccess]` - Minimum Editor access  
- `[RequireAdminAccess]` - Admin access required
- `[ProjectAccess(AccessType.X, "paramName")]` - Custom access level and parameter name

### 2. Attribute Features

- Automatic project ID extraction from route or body parameters
- Flexible parameter name specification
- Proper HTTP status code responses (403, 404, 500)
- Integration with existing authentication system

## Integration with Existing Systems

### 1. Project Locking

The access control system integrates seamlessly with project locking:

```typescript
// In project.service.ts
private handleProjectAccess(lockStatus: LockResult, project: ProjectDetails, loggedInUser: IUser | null): void {
  if (!lockStatus.isProjectLocked) {
    this.accessService.setProjectAccess(project.accessType);
    return;
  }

  // If locked by another user, downgrade to Viewer access
  if (isCurrentUserLocking) {
    this.accessService.setProjectAccess(AccessType.Viewer);
    // Show lock notification
  } else {
    this.accessService.setProjectAccess(project.accessType);
  }
}
```

### 2. Authentication

Works with existing cookie-based authentication:
- Uses HTTP-only cookies for token storage
- Integrates with JWT bearer authentication
- Handles token refresh automatically

## Error Handling

### Frontend Errors

- **403 Forbidden** - User lacks required permissions
- **404 Not Found** - Project doesn't exist or no access
- **500 Server Error** - Permission check failed

All errors redirect to dashboard with appropriate user messages.

### Backend Errors

- **400 Bad Request** - Missing or invalid project ID
- **401 Unauthorized** - User not authenticated
- **403 Forbidden** - Insufficient permissions
- **404 Not Found** - Project not found or no access
- **500 Internal Server Error** - System error

## Testing

Comprehensive test suites are provided:

### Frontend Tests
- `access.guard.spec.ts` - Route guard functionality
- `project-access.service.spec.ts` - Service layer testing

### Running Tests
```bash
# Frontend tests
ng test

# Backend tests
dotnet test
```

## Performance Considerations

### Caching Strategy
- Frontend caches permissions per project
- Cache invalidation on permission changes
- Bulk validation for multiple projects

### API Optimization
- Single permission check endpoint
- Efficient database queries
- Minimal network requests

## Security Best Practices

1. **Defense in Depth** - Both frontend and backend validation
2. **Principle of Least Privilege** - Users get minimum required access
3. **Fail Secure** - Deny access on errors
4. **Audit Trail** - Log permission checks and failures
5. **Regular Review** - Periodic permission audits

## Migration Guide

For existing projects, the system is backward compatible:

1. Existing routes continue to work
2. New protection is opt-in via guard application
3. Backend attributes can be added incrementally
4. No breaking changes to existing APIs

## Troubleshooting

### Common Issues

1. **Guard not triggering** - Ensure `accessGuard` is added to route configuration
2. **Permission cache stale** - Use `refreshProjectPermission()` after changes
3. **Backend attribute not working** - Verify project ID parameter name matches
4. **404 on valid project** - Check user has at least Viewer access

### Debug Tools

```typescript
// Enable debug logging
this.projectAccessService.getCachedPermissions().subscribe(cache => {
  console.log('Cached permissions:', cache);
});
```

## Future Enhancements

Planned improvements:
- Role-based permissions beyond project level
- Time-based access controls
- Audit logging dashboard
- Permission delegation
- API rate limiting based on access level

## Support

For questions or issues:
1. Check this documentation
2. Review test files for usage examples
3. Consult the existing codebase patterns
4. Contact the development team
