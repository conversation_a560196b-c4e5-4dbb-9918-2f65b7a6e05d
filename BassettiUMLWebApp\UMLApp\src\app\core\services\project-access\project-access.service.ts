import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { AccessType, IProjectPermissionDTO } from 'src/app/shared/model/project';
import { PermissionApiService } from '../api/permission-api.service';

@Injectable({
  providedIn: 'root',
})
export class ProjectAccessService {
  private _currentProjectPermissions = new BehaviorSubject<Map<number, IProjectPermissionDTO>>(new Map());

  constructor(private permissionApiService: PermissionApiService) {}

  /**
   * Checks if the current user has the required access level for a specific project
   * @param projectId - The ID of the project to check
   * @param requiredAccess - The minimum access level required
   * @returns Observable<boolean> indicating if the user has sufficient access
   */
  hasProjectAccess(projectId: number, requiredAccess: AccessType): Observable<boolean> {
    return this.getProjectPermission(projectId).pipe(
      map(permission => {
        if (!permission) {
          return false;
        }
        return this.hasRequiredAccess(permission.accessType, requiredAccess);
      }),
      catchError(() => of(false))
    );
  }

  /**
   * Gets the user's permission for a specific project
   * @param projectId - The ID of the project
   * @returns Observable<IProjectPermissionDTO | null>
   */
  getProjectPermission(projectId: number): Observable<IProjectPermissionDTO | null> {
    // Check if we already have the permission cached
    const cachedPermissions = this._currentProjectPermissions.value;
    if (cachedPermissions.has(projectId)) {
      return of(cachedPermissions.get(projectId)!);
    }

    // Fetch permission from API
    return this.permissionApiService.checkPermission(projectId).pipe(
      tap(permission => {
        // Cache the permission
        const updatedPermissions = new Map(cachedPermissions);
        updatedPermissions.set(projectId, permission);
        this._currentProjectPermissions.next(updatedPermissions);
      }),
      catchError(error => {
        console.error(`Error fetching permission for project ${projectId}:`, error);
        return of(null);
      })
    );
  }

  /**
   * Checks if the user can view a project
   * @param projectId - The ID of the project
   * @returns Observable<boolean>
   */
  canViewProject(projectId: number): Observable<boolean> {
    return this.hasProjectAccess(projectId, AccessType.Viewer);
  }

  /**
   * Checks if the user can edit a project
   * @param projectId - The ID of the project
   * @returns Observable<boolean>
   */
  canEditProject(projectId: number): Observable<boolean> {
    return this.hasProjectAccess(projectId, AccessType.Editor);
  }

  /**
   * Checks if the user can administer a project
   * @param projectId - The ID of the project
   * @returns Observable<boolean>
   */
  canAdminProject(projectId: number): Observable<boolean> {
    return this.hasProjectAccess(projectId, AccessType.Admin);
  }

  /**
   * Clears cached permissions for a specific project
   * @param projectId - The ID of the project to clear from cache
   */
  clearProjectPermissionCache(projectId: number): void {
    const cachedPermissions = this._currentProjectPermissions.value;
    if (cachedPermissions.has(projectId)) {
      const updatedPermissions = new Map(cachedPermissions);
      updatedPermissions.delete(projectId);
      this._currentProjectPermissions.next(updatedPermissions);
    }
  }

  /**
   * Clears all cached permissions
   */
  clearAllPermissionCache(): void {
    this._currentProjectPermissions.next(new Map());
  }

  /**
   * Gets the current cached permissions
   * @returns Observable<Map<number, IProjectPermissionDTO>>
   */
  getCachedPermissions(): Observable<Map<number, IProjectPermissionDTO>> {
    return this._currentProjectPermissions.asObservable();
  }

  /**
   * Checks if the user's access type meets the required access level
   * @param userAccess - The user's current access level
   * @param requiredAccess - The required access level
   * @returns boolean indicating if access is sufficient
   */
  private hasRequiredAccess(userAccess: AccessType, requiredAccess: AccessType): boolean {
    // Access hierarchy: Admin (0) > Editor (1) > Viewer (2)
    // Lower numeric values have higher access
    return userAccess <= requiredAccess;
  }

  /**
   * Validates multiple project permissions at once
   * @param projectPermissions - Array of objects containing projectId and requiredAccess
   * @returns Observable<Map<number, boolean>> mapping project IDs to access results
   */
  validateMultipleProjectAccess(
    projectPermissions: Array<{ projectId: number; requiredAccess: AccessType }>
  ): Observable<Map<number, boolean>> {
    const accessChecks = projectPermissions.map(({ projectId, requiredAccess }) =>
      this.hasProjectAccess(projectId, requiredAccess).pipe(
        map(hasAccess => ({ projectId, hasAccess }))
      )
    );

    return new Observable(observer => {
      const results = new Map<number, boolean>();
      let completed = 0;

      accessChecks.forEach(check => {
        check.subscribe({
          next: ({ projectId, hasAccess }) => {
            results.set(projectId, hasAccess);
            completed++;
            if (completed === accessChecks.length) {
              observer.next(results);
              observer.complete();
            }
          },
          error: (error) => {
            console.error('Error in multiple access validation:', error);
            observer.error(error);
          }
        });
      });
    });
  }

  /**
   * Refreshes permission for a specific project by clearing cache and fetching fresh data
   * @param projectId - The ID of the project to refresh
   * @returns Observable<IProjectPermissionDTO | null>
   */
  refreshProjectPermission(projectId: number): Observable<IProjectPermissionDTO | null> {
    this.clearProjectPermissionCache(projectId);
    return this.getProjectPermission(projectId);
  }
}
