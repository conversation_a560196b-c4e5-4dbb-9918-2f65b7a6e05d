import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import {
  AccessType,
  IProjectPermissionDTO,
} from 'src/app/shared/model/project';
import { PermissionApiService } from '../services/api/permission-api.service';
import { ErrorService } from '../services/errors/error.service';
import { accessGuard } from './access.guard';

describe('accessGuard', () => {
  let mockRouter: jasmine.SpyObj<Router>;
  let mockPermissionApiService: jasmine.SpyObj<PermissionApiService>;
  let mockErrorService: jasmine.SpyObj<ErrorService>;

  const mockPermission: IProjectPermissionDTO = {
    id: 1,
    accessType: AccessType.Editor,
    email: '<EMAIL>',
  };

  beforeEach(() => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const permissionSpy = jasmine.createSpyObj('PermissionApiService', [
      'checkPermission',
    ]);
    const errorSpy = jasmine.createSpyObj('ErrorService', ['addError']);

    TestBed.configureTestingModule({
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: PermissionApiService, useValue: permissionSpy },
        { provide: ErrorService, useValue: errorSpy },
      ],
    });

    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockPermissionApiService = TestBed.inject(
      PermissionApiService
    ) as jasmine.SpyObj<PermissionApiService>;
    mockErrorService = TestBed.inject(
      ErrorService
    ) as jasmine.SpyObj<ErrorService>;
  });

  it('should be created', () => {
    expect(accessGuard).toBeTruthy();
  });

  it('should allow access when no project ID is provided', (done) => {
    const mockRoute = {
      params: {},
      routeConfig: { path: 'dashboard' },
    } as any;

    const result = TestBed.runInInjectionContext(() =>
      accessGuard(mockRoute, {} as any)
    );

    expect(result).toBe(true);
    done();
  });

  it('should allow access when user has sufficient permissions for editor route', (done) => {
    mockPermissionApiService.checkPermission.and.returnValue(
      of(mockPermission)
    );

    const mockRoute = {
      params: { id: '1' },
      routeConfig: { path: 'editor/:id/diagram/:idDiagram' },
    } as any;

    const result = TestBed.runInInjectionContext(() =>
      accessGuard(mockRoute, {} as any)
    );

    if (typeof result === 'boolean') {
      expect(result).toBe(true);
      done();
    } else {
      result.subscribe((canActivate) => {
        expect(canActivate).toBe(true);
        expect(mockPermissionApiService.checkPermission).toHaveBeenCalledWith(
          1
        );
        done();
      });
    }
  });

  it('should deny access when user has insufficient permissions for editor route', (done) => {
    const viewerPermission: IProjectPermissionDTO = {
      ...mockPermission,
      accessType: AccessType.Viewer,
    };
    mockPermissionApiService.checkPermission.and.returnValue(
      of(viewerPermission)
    );

    const mockRoute = {
      params: { id: '1' },
      routeConfig: { path: 'editor/:id/diagram/:idDiagram' },
    } as any;

    const result = TestBed.runInInjectionContext(() =>
      accessGuard(mockRoute, {} as any)
    );

    if (typeof result === 'boolean') {
      expect(result).toBe(false);
      done();
    } else {
      result.subscribe((canActivate) => {
        expect(canActivate).toBe(false);
        expect(mockRouter.navigate).toHaveBeenCalledWith(['/dashboard']);
        expect(mockErrorService.addError).toHaveBeenCalledWith({
          errorKey: 403,
          type: 'error',
          header: 'Access Denied',
          content: 'You need editor access to modify this project.',
          isCustomError: true,
        });
        done();
      });
    }
  });

  it('should handle 404 errors appropriately', (done) => {
    const error = { status: 404 };
    mockPermissionApiService.checkPermission.and.returnValue(throwError(error));

    const mockRoute = {
      params: { id: '1' },
      routeConfig: { path: 'editor/:id/diagram/:idDiagram' },
    } as any;

    const result = TestBed.runInInjectionContext(() =>
      accessGuard(mockRoute, {} as any)
    );

    if (typeof result === 'boolean') {
      expect(result).toBe(false);
      done();
    } else {
      result.subscribe((canActivate) => {
        expect(canActivate).toBe(false);
        expect(mockRouter.navigate).toHaveBeenCalledWith(['/dashboard']);
        expect(mockErrorService.addError).toHaveBeenCalledWith({
          errorKey: 404,
          type: 'error',
          header: 'Project Not Found',
          content:
            'The requested project was not found or you do not have access to it.',
          isCustomError: true,
        });
        done();
      });
    }
  });
});
