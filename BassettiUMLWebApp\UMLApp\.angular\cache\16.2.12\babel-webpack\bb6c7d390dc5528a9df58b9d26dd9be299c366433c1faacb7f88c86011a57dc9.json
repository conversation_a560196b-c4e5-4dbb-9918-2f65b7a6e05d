{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { catchError, map, of } from 'rxjs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport { PermissionApiService } from '../services/api/permission-api.service';\nimport { ErrorService } from '../services/errors/error.service';\nexport const accessGuard = (route, _state) => {\n  const permissionApiService = inject(PermissionApiService);\n  const router = inject(Router);\n  const errorService = inject(ErrorService);\n  const navigation = history.state?.navigationId;\n  if (!navigation) {\n    localStorage.setItem('copyUrl', 'true');\n  }\n  // Extract project ID from route parameters\n  const projectId = route.params['id'];\n  if (!projectId) {\n    // If no project ID, allow access (for dashboard, etc.)\n    return true;\n  }\n  // Determine required access level based on route\n  const requiredAccessLevel = getRequiredAccessLevel(route.routeConfig?.path || '');\n  // Check user permission for the project\n  return permissionApiService.checkPermission(Number(projectId)).pipe(map(permission => {\n    // Check if user has sufficient permission level\n    if (permission && hasRequiredAccess(permission.accessType, requiredAccessLevel)) {\n      return true;\n    } else {\n      // User doesn't have sufficient permission, redirect to dashboard\n      router.navigate(['/dashboard']);\n      errorService.addError({\n        errorKey: 403,\n        type: 'error',\n        header: 'Access Denied',\n        content: getAccessDeniedMessage(requiredAccessLevel),\n        isCustomError: true\n      });\n      return false;\n    }\n  }), catchError(error => {\n    // Handle permission check error\n    console.error('Error checking project permission:', error);\n    if (error.status === 404) {\n      // Project not found or user has no permission\n      router.navigate(['/dashboard']);\n      errorService.addError({\n        errorKey: 404,\n        type: 'error',\n        header: 'Project Not Found',\n        content: 'The requested project was not found or you do not have access to it.',\n        isCustomError: true\n      });\n    } else {\n      // Other errors\n      router.navigate(['/dashboard']);\n      errorService.addError({\n        errorKey: 500,\n        type: 'error',\n        header: 'Access Check Failed',\n        content: 'Unable to verify project access. Please try again.',\n        isCustomError: true\n      });\n    }\n    return of(false);\n  }));\n};\n/**\n * Determines the required access level based on the route path\n */\nfunction getRequiredAccessLevel(routePath) {\n  // Editor routes require at least Editor access (WRITE)\n  if (routePath.includes('editor')) {\n    return AccessType.Editor;\n  }\n  // Add more route-specific access requirements here as needed\n  // For example:\n  // if (routePath.includes('admin')) {\n  //   return AccessType.Admin;\n  // }\n  // Default to Viewer access for other routes\n  return AccessType.Viewer;\n}\n/**\n * Checks if the user's access type meets the required access level\n */\nfunction hasRequiredAccess(userAccess, requiredAccess) {\n  // Access hierarchy: Admin (0) > Editor (1) > Viewer (2)\n  // Lower numeric values have higher access\n  return userAccess <= requiredAccess;\n}\n/**\n * Gets an appropriate error message based on the required access level\n */\nfunction getAccessDeniedMessage(requiredAccess) {\n  switch (requiredAccess) {\n    case AccessType.Admin:\n      return 'You need administrator access to perform this action.';\n    case AccessType.Editor:\n      return 'You need editor access to modify this project.';\n    case AccessType.Viewer:\n      return 'You do not have permission to access this project.';\n    default:\n      return 'You do not have sufficient permissions to access this resource.';\n  }\n}", "map": {"version": 3, "names": ["inject", "Router", "catchError", "map", "of", "AccessType", "PermissionApiService", "ErrorService", "accessGuard", "route", "_state", "permissionApiService", "router", "errorService", "navigation", "history", "state", "navigationId", "localStorage", "setItem", "projectId", "params", "requiredAccessLevel", "getRequiredAccessLevel", "routeConfig", "path", "checkPermission", "Number", "pipe", "permission", "hasRequiredAccess", "accessType", "navigate", "addError", "<PERSON><PERSON><PERSON>", "type", "header", "content", "getAccessDeniedMessage", "isCustomError", "error", "console", "status", "routePath", "includes", "Editor", "Viewer", "userAccess", "requiredAccess", "Admin"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\guards\\access.guard.ts"], "sourcesContent": ["import { inject } from '@angular/core';\r\nimport { CanActivateFn, Router } from '@angular/router';\r\nimport { catchError, map, of } from 'rxjs';\r\nimport { AccessType } from 'src/app/shared/model/project';\r\nimport { PermissionApiService } from '../services/api/permission-api.service';\r\nimport { ErrorService } from '../services/errors/error.service';\r\n\r\nexport const accessGuard: CanActivateFn = (route, _state) => {\r\n  const permissionApiService = inject(PermissionApiService);\r\n  const router = inject(Router);\r\n  const errorService = inject(ErrorService);\r\n\r\n  const navigation = history.state?.navigationId;\r\n  if (!navigation) {\r\n    localStorage.setItem('copyUrl', 'true');\r\n  }\r\n\r\n  // Extract project ID from route parameters\r\n  const projectId = route.params['id'];\r\n\r\n  if (!projectId) {\r\n    // If no project ID, allow access (for dashboard, etc.)\r\n    return true;\r\n  }\r\n\r\n  // Determine required access level based on route\r\n  const requiredAccessLevel = getRequiredAccessLevel(\r\n    route.routeConfig?.path || ''\r\n  );\r\n\r\n  // Check user permission for the project\r\n  return permissionApiService.checkPermission(Number(projectId)).pipe(\r\n    map((permission) => {\r\n      // Check if user has sufficient permission level\r\n      if (\r\n        permission &&\r\n        hasRequiredAccess(permission.accessType, requiredAccessLevel)\r\n      ) {\r\n        return true;\r\n      } else {\r\n        // User doesn't have sufficient permission, redirect to dashboard\r\n        router.navigate(['/dashboard']);\r\n        errorService.addError({\r\n          errorKey: 403,\r\n          type: 'error',\r\n          header: 'Access Denied',\r\n          content: getAccessDeniedMessage(requiredAccessLevel),\r\n          isCustomError: true,\r\n        });\r\n        return false;\r\n      }\r\n    }),\r\n    catchError((error) => {\r\n      // Handle permission check error\r\n      console.error('Error checking project permission:', error);\r\n\r\n      if (error.status === 404) {\r\n        // Project not found or user has no permission\r\n        router.navigate(['/dashboard']);\r\n        errorService.addError({\r\n          errorKey: 404,\r\n          type: 'error',\r\n          header: 'Project Not Found',\r\n          content:\r\n            'The requested project was not found or you do not have access to it.',\r\n          isCustomError: true,\r\n        });\r\n      } else {\r\n        // Other errors\r\n        router.navigate(['/dashboard']);\r\n        errorService.addError({\r\n          errorKey: 500,\r\n          type: 'error',\r\n          header: 'Access Check Failed',\r\n          content: 'Unable to verify project access. Please try again.',\r\n          isCustomError: true,\r\n        });\r\n      }\r\n\r\n      return of(false);\r\n    })\r\n  );\r\n};\r\n\r\n/**\r\n * Determines the required access level based on the route path\r\n */\r\nfunction getRequiredAccessLevel(routePath: string): AccessType {\r\n  // Editor routes require at least Editor access (WRITE)\r\n  if (routePath.includes('editor')) {\r\n    return AccessType.Editor;\r\n  }\r\n\r\n  // Add more route-specific access requirements here as needed\r\n  // For example:\r\n  // if (routePath.includes('admin')) {\r\n  //   return AccessType.Admin;\r\n  // }\r\n\r\n  // Default to Viewer access for other routes\r\n  return AccessType.Viewer;\r\n}\r\n\r\n/**\r\n * Checks if the user's access type meets the required access level\r\n */\r\nfunction hasRequiredAccess(\r\n  userAccess: AccessType,\r\n  requiredAccess: AccessType\r\n): boolean {\r\n  // Access hierarchy: Admin (0) > Editor (1) > Viewer (2)\r\n  // Lower numeric values have higher access\r\n  return userAccess <= requiredAccess;\r\n}\r\n\r\n/**\r\n * Gets an appropriate error message based on the required access level\r\n */\r\nfunction getAccessDeniedMessage(requiredAccess: AccessType): string {\r\n  switch (requiredAccess) {\r\n    case AccessType.Admin:\r\n      return 'You need administrator access to perform this action.';\r\n    case AccessType.Editor:\r\n      return 'You need editor access to modify this project.';\r\n    case AccessType.Viewer:\r\n      return 'You do not have permission to access this project.';\r\n    default:\r\n      return 'You do not have sufficient permissions to access this resource.';\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAAwBC,MAAM,QAAQ,iBAAiB;AACvD,SAASC,UAAU,EAAEC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AAC1C,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,YAAY,QAAQ,kCAAkC;AAE/D,OAAO,MAAMC,WAAW,GAAkBA,CAACC,KAAK,EAAEC,MAAM,KAAI;EAC1D,MAAMC,oBAAoB,GAAGX,MAAM,CAACM,oBAAoB,CAAC;EACzD,MAAMM,MAAM,GAAGZ,MAAM,CAACC,MAAM,CAAC;EAC7B,MAAMY,YAAY,GAAGb,MAAM,CAACO,YAAY,CAAC;EAEzC,MAAMO,UAAU,GAAGC,OAAO,CAACC,KAAK,EAAEC,YAAY;EAC9C,IAAI,CAACH,UAAU,EAAE;IACfI,YAAY,CAACC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;;EAGzC;EACA,MAAMC,SAAS,GAAGX,KAAK,CAACY,MAAM,CAAC,IAAI,CAAC;EAEpC,IAAI,CAACD,SAAS,EAAE;IACd;IACA,OAAO,IAAI;;EAGb;EACA,MAAME,mBAAmB,GAAGC,sBAAsB,CAChDd,KAAK,CAACe,WAAW,EAAEC,IAAI,IAAI,EAAE,CAC9B;EAED;EACA,OAAOd,oBAAoB,CAACe,eAAe,CAACC,MAAM,CAACP,SAAS,CAAC,CAAC,CAACQ,IAAI,CACjEzB,GAAG,CAAE0B,UAAU,IAAI;IACjB;IACA,IACEA,UAAU,IACVC,iBAAiB,CAACD,UAAU,CAACE,UAAU,EAAET,mBAAmB,CAAC,EAC7D;MACA,OAAO,IAAI;KACZ,MAAM;MACL;MACAV,MAAM,CAACoB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;MAC/BnB,YAAY,CAACoB,QAAQ,CAAC;QACpBC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE,eAAe;QACvBC,OAAO,EAAEC,sBAAsB,CAAChB,mBAAmB,CAAC;QACpDiB,aAAa,EAAE;OAChB,CAAC;MACF,OAAO,KAAK;;EAEhB,CAAC,CAAC,EACFrC,UAAU,CAAEsC,KAAK,IAAI;IACnB;IACAC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAE1D,IAAIA,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;MACxB;MACA9B,MAAM,CAACoB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;MAC/BnB,YAAY,CAACoB,QAAQ,CAAC;QACpBC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE,mBAAmB;QAC3BC,OAAO,EACL,sEAAsE;QACxEE,aAAa,EAAE;OAChB,CAAC;KACH,MAAM;MACL;MACA3B,MAAM,CAACoB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;MAC/BnB,YAAY,CAACoB,QAAQ,CAAC;QACpBC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE,qBAAqB;QAC7BC,OAAO,EAAE,oDAAoD;QAC7DE,aAAa,EAAE;OAChB,CAAC;;IAGJ,OAAOnC,EAAE,CAAC,KAAK,CAAC;EAClB,CAAC,CAAC,CACH;AACH,CAAC;AAED;;;AAGA,SAASmB,sBAAsBA,CAACoB,SAAiB;EAC/C;EACA,IAAIA,SAAS,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;IAChC,OAAOvC,UAAU,CAACwC,MAAM;;EAG1B;EACA;EACA;EACA;EACA;EAEA;EACA,OAAOxC,UAAU,CAACyC,MAAM;AAC1B;AAEA;;;AAGA,SAAShB,iBAAiBA,CACxBiB,UAAsB,EACtBC,cAA0B;EAE1B;EACA;EACA,OAAOD,UAAU,IAAIC,cAAc;AACrC;AAEA;;;AAGA,SAASV,sBAAsBA,CAACU,cAA0B;EACxD,QAAQA,cAAc;IACpB,KAAK3C,UAAU,CAAC4C,KAAK;MACnB,OAAO,uDAAuD;IAChE,KAAK5C,UAAU,CAACwC,MAAM;MACpB,OAAO,gDAAgD;IACzD,KAAKxC,UAAU,CAACyC,MAAM;MACpB,OAAO,oDAAoD;IAC7D;MACE,OAAO,iEAAiE;;AAE9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}