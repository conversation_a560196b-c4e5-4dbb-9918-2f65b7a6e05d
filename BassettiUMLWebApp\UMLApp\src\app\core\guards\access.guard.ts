import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { catchError, map, of } from 'rxjs';
import { AccessType } from 'src/app/shared/model/project';
import { PermissionApiService } from '../services/api/permission-api.service';
import { ErrorService } from '../services/errors/error.service';

export const accessGuard: CanActivateFn = (route, _state) => {
  const permissionApiService = inject(PermissionApiService);
  const router = inject(Router);
  const errorService = inject(ErrorService);

  const navigation = history.state?.navigationId;
  if (!navigation) {
    localStorage.setItem('copyUrl', 'true');
  }

  // Extract project ID from route parameters
  const projectId = route.params['id'];

  if (!projectId) {
    // If no project ID, allow access (for dashboard, etc.)
    return true;
  }

  // Determine required access level based on route
  const requiredAccessLevel = getRequiredAccessLevel(
    route.routeConfig?.path || ''
  );

  // Check user permission for the project
  return permissionApiService.checkPermission(Number(projectId)).pipe(
    map((permission) => {
      // Check if user has sufficient permission level
      if (
        permission &&
        hasRequiredAccess(permission.accessType, requiredAccessLevel)
      ) {
        return true;
      } else {
        // User doesn't have sufficient permission, redirect to dashboard
        router.navigate(['/dashboard']);
        errorService.addError({
          errorKey: 403,
          type: 'error',
          header: 'Access Denied',
          content: getAccessDeniedMessage(requiredAccessLevel),
          isCustomError: true,
        });
        return false;
      }
    }),
    catchError((error) => {
      // Handle permission check error
      console.error('Error checking project permission:', error);

      if (error.status === 404) {
        // Project not found or user has no permission
        router.navigate(['/dashboard']);
        errorService.addError({
          errorKey: 404,
          type: 'error',
          header: 'Project Not Found',
          content:
            'The requested project was not found or you do not have access to it.',
          isCustomError: true,
        });
      } else {
        // Other errors
        router.navigate(['/dashboard']);
        errorService.addError({
          errorKey: 500,
          type: 'error',
          header: 'Access Check Failed',
          content: 'Unable to verify project access. Please try again.',
          isCustomError: true,
        });
      }

      return of(false);
    })
  );
};

/**
 * Determines the required access level based on the route path
 */
function getRequiredAccessLevel(routePath: string): AccessType {
  // Editor routes require at least Editor access (WRITE)
  if (routePath.includes('editor')) {
    return AccessType.Editor;
  }

  // Add more route-specific access requirements here as needed
  // For example:
  // if (routePath.includes('admin')) {
  //   return AccessType.Admin;
  // }

  // Default to Viewer access for other routes
  return AccessType.Viewer;
}

/**
 * Checks if the user's access type meets the required access level
 */
function hasRequiredAccess(
  userAccess: AccessType,
  requiredAccess: AccessType
): boolean {
  // Access hierarchy: Admin (0) > Editor (1) > Viewer (2)
  // Lower numeric values have higher access
  return userAccess <= requiredAccess;
}

/**
 * Gets an appropriate error message based on the required access level
 */
function getAccessDeniedMessage(requiredAccess: AccessType): string {
  switch (requiredAccess) {
    case AccessType.Admin:
      return 'You need administrator access to perform this action.';
    case AccessType.Editor:
      return 'You need editor access to modify this project.';
    case AccessType.Viewer:
      return 'You do not have permission to access this project.';
    default:
      return 'You do not have sufficient permissions to access this resource.';
  }
}
